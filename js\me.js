document.addEventListener("DOMContentLoaded",function(){let e=document.querySelectorAll(".single-slider"),t=document.querySelector(".slider-nav.prev"),l=document.querySelector(".slider-nav.next"),i=document.querySelector(".slider-dots"),c=0;e.forEach((e,t)=>{let l=document.createElement("div");l.classList.add("dot"),0===t&&l.classList.add("active"),l.addEventListener("click",()=>s(t)),i.appendChild(l)});let d=document.querySelectorAll(".dot");function s(t){e[c].classList.remove("active"),d[c].classList.remove("active"),e[t].classList.add("active"),d[t].classList.add("active"),c=t}function n(){s((c+1)%e.length)}l.addEventListener("click",n),t.addEventListener("click",function t(){s((c-1+e.length)%e.length)}),setInterval(n,5e3)});